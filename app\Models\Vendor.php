<?php
namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Vendor extends Model
{
    use HasFactory;
    protected $fillable = [
        'user_id',
        'shop_name',
        'slug',
        'description',
        'address',
        'city',
        'state',
        'country',
        'logo',
        'approved',
        'is_featured',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
    
    public function products()
    {
        return $this->hasMany(Product::class);
    }
    
    public function commissions()
    {
        return $this->hasMany(Commission::class);
    }

    public function reviews()
    {
        return $this->hasMany(Review::class);
    }
    
    public function subscription()
    {
        return $this->hasOneThrough(
            Subscription::class,
            User::class,
            'id', // Foreign key on users table...
            'user_id', // Foreign key on subscriptions table...
            'user_id', // Local key on vendors table...
            'id' // Local key on users table...
        );
    }
    
    public function getRouteKeyName()
    {
        return 'slug';
    }
    
    public function getTotalSalesAttribute()
    {
        return $this->products()
            ->withSum('orderItems as total_sales', 'price_at_purchase * quantity')
            ->get()
            ->sum('total_sales');
    }

    /**
     * Check if vendor is in platform delivery zone
     */
    public function isPlatformDeliveryZone(): bool
    {
        return $this->delivery_zone_type === 'platform_delivery_zone';
    }

    /**
     * Check if vendor has active subscription
     */
    public function hasActiveSubscription(): bool
    {
        return $this->subscription_status === 'active';
    }

    /**
     * Get vendor's business name or shop name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->business_name ?: $this->shop_name;
    }

    /**
     * Get vendor's primary address
     */
    public function getPrimaryAddressAttribute(): string
    {
        return $this->address_line1 ?: $this->address;
    }

    /**
     * Get vendor's contact email
     */
    public function getContactEmailAttribute(): string
    {
        return $this->contact_email_business ?: $this->user->email;
    }

    /**
     * Get brand name (same as shop name since vendors are brands)
     */
    public function getBrandNameAttribute(): string
    {
        return $this->shop_name;
    }

    /**
     * Get brand description (use brand_description if available, otherwise description)
     */
    public function getBrandDescriptionAttribute(): string
    {
        return $this->brand_description ?: $this->description;
    }

    /**
     * Get brand logo (use brand_logo if available, otherwise logo)
     */
    public function getBrandLogoAttribute(): string
    {
        return $this->brand_logo ?: $this->logo;
    }

    /**
     * Scope for featured brands (vendors)
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for active brands (vendors)
     */
    public function scopeActive($query)
    {
        return $query->where('approved', true);
    }
}
