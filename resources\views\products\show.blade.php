@extends('layouts.app')

@section('content')
    <div class="container-fluid py-5 px-3 px-sm-4 px-md-5">
        <div class="row mb-4">
            <div class="col-12">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}"
                                class="text-decoration-none text-dark">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('products.index') }}"
                                class="text-decoration-none text-dark">Shop</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('products.category', $product->category->slug) }}"
                                class="text-decoration-none text-dark">{{ $product->category->name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ $product->name }}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row g-4 align-items-start">
        <!-- Product Images -->
        <div class="col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body p-0">
                    <div class="position-relative">
                        @if (
                            $product->image_url &&
                                (filter_var($product->image_url, FILTER_VALIDATE_URL) || file_exists(public_path($product->image_url))))
                            <img src="{{ $product->image_url }}" alt="{{ $product->name }}" class="img-fluid rounded"
                                id="main-image">
                        @else
                            <img src="https://via.placeholder.com/800x600?text={{ urlencode($product->name) }}"
                                alt="{{ $product->name }}" class="img-fluid rounded" id="main-image">
                        @endif

                        @if ($product->isOnSale())
                            <div class="position-absolute top-0 start-0 bg-dark text-white fw-bold px-3 py-2 m-3 rounded">
                                SALE</div>
                        @endif
                    </div>

                    <div class="d-flex mt-3 px-3 pb-3">
                        @if ($product->image_url)
                            <div class="me-2">
                                <img src="{{ $product->image_url ?? 'https://via.placeholder.com/100x100?text=' . urlencode($product->name) }}"
                                    alt="{{ $product->name }} thumbnail" class="img-fluid rounded thumbnail active"
                                    style="width: 80px; height: 80px; object-fit: cover; cursor: pointer;">
                            </div>
                        @endif
                        {{-- If you have an array/collection $product->galleryImages, you would loop through them here --}}
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Details -->
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body p-4">
                    <h1 class="fw-bold mb-2">{{ $product->name }}</h1>

                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            @php
                                $fullStars = floor($averageRating);
                                $hasHalfStar = $averageRating - $fullStars >= 0.5;
                                $emptyStars = 5 - $fullStars - ($hasHalfStar ? 1 : 0);
                            @endphp

                            @for ($i = 0; $i < $fullStars; $i++)
                                <i class="fas fa-star text-warning"></i>
                            @endfor

                            @if ($hasHalfStar)
                                <i class="fas fa-star-half-alt text-warning"></i>
                            @endif

                            @for ($i = 0; $i < $emptyStars; $i++)
                                <i class="far fa-star text-warning"></i>
                            @endfor

                            <span class="ms-1">{{ number_format($averageRating, 1) }}</span>
                        </div>
                        <span class="text-muted">({{ $reviewCount }}
                            {{ $reviewCount == 1 ? 'review' : 'reviews' }})</span>
                    </div>

                    <div class="mb-4">
                        @if ($product->isOnSale())
                            <div class="d-flex align-items-center">
                                <h3 class="fw-bold me-3 mb-0">₦{{ number_format($product->discount_price, 2) }}</h3>
                                <h5 class="text-muted text-decoration-line-through mb-0">
                                    ₦{{ number_format($product->price, 2) }}</h5>
                                <span class="badge bg-dark ms-3">{{ $product->getDiscountPercentage() }}% OFF</span>
                            </div>
                        @else
                            <h3 class="fw-bold mb-0">₦{{ number_format($product->price, 2) }}</h3>
                        @endif
                    </div>

                    <div class="mb-4">
                        <p class="mb-0">{{ $product->description }}</p>
                    </div>

                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-3 fw-bold">Availability:</span>
                            @if ($product->stock_quantity > 0)
                                <span class="text-success">In Stock ({{ $product->stock_quantity }} available)</span>
                            @else
                                <span class="text-danger">Out of Stock</span>
                            @endif
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-3 fw-bold">Category:</span>
                            <a href="{{ route('products.category', $product->category->slug) }}"
                                class="text-decoration-none">{{ $product->category->name }}</a>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-3 fw-bold">Brand:</span>
                            <span>{{ $product->brand->name ?? 'N/A' }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="me-3 fw-bold">Vendor:</span>
                            <a href="{{ route('vendors.storefront', $product->vendor->slug) }}"
                                class="text-decoration-none">{{ $product->vendor->shop_name }}</a>
                        </div>
                    </div>

                    <hr>

                    <form action="{{ route('cart.add', $product->id) }}" method="POST" class="ajax-add-to-cart-form">
                        @csrf
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <span class="me-3 fw-bold">Quantity:</span>
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-outline-dark px-3"
                                        id="decrease-quantity">-</button>
                                    <input type="number" class="form-control text-center mx-2" name="quantity"
                                        id="quantity" value="1" min="1"
                                        max="{{ $product->stock_quantity > 0 ? $product->stock_quantity : 1 }}"
                                        style="width: 60px;" {{ $product->stock_quantity == 0 ? 'disabled' : '' }}>
                                    <button type="button" class="btn btn-outline-dark px-3"
                                        id="increase-quantity">+</button>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-dark py-3 fw-bold add-to-cart-btn" id="add-to-cart"
                                {{ $product->stock_quantity == 0 ? 'disabled' : '' }}>
                                <i class="fas fa-shopping-cart me-2"></i>
                                {{ $product->stock_quantity == 0 ? 'Out of Stock' : 'Add to Cart' }}
                            </button>
                            <button type="button" class="btn btn-outline-dark py-3 fw-bold" id="add-to-wishlist">
                                <i class="far fa-heart me-2"></i> Add to Wishlist
                            </button>
                        </div>
                    </form>

                    <div class="d-flex justify-content-center flex-wrap gap-3">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-truck me-2"></i>
                            <span>Free Shipping</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-undo me-2"></i>
                            <span>30-Day Returns</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-shield-alt me-2"></i>
                            <span>Secure Checkout</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}"
                            class="text-decoration-none text-dark">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('products.index') }}"
                            class="text-decoration-none text-dark">Shop</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('products.category', $product->category->slug) }}"
                            class="text-decoration-none text-dark">{{ $product->category->name }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">{{ $product->name }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row">
        <!-- Product Images -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <div class="position-relative">
                        <img src="{{ $product->image_url ?? asset('images/default-product.png') }}"
                            alt="{{ $product->name }}" class="img-fluid rounded" id="main-image">
                        @if ($product->isOnSale())
                            <div class="position-absolute top-0 start-0 bg-dark text-white fw-bold px-3 py-2 m-3 rounded">
                                SALE</div>
                        @endif
                    </div>

                    <div class="d-flex mt-3 px-3 pb-3">
                        @if ($product->image_url)
                            <div class="me-2">
                                <img src="{{ $product->image_url ?? asset('images/default-product.png') }}"
                                    alt="{{ $product->name }} thumbnail" class="img-fluid rounded thumbnail active"
                                    style="width: 80px; height: 80px; object-fit: cover; cursor: pointer;">
                            </div>
                        @endif
                        {{-- If you have an array/collection $product->galleryImages, you would loop through them here --}}
                    </div>
                </div>
            </div>
        </div>

        <!-- Product Details -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <h1 class="fw-bold mb-2">{{ $product->name }}</h1>

                    <div class="d-flex align-items-center mb-3">
                        <div class="me-3">
                            @php
                                $fullStars = floor($averageRating);
                                $hasHalfStar = $averageRating - $fullStars >= 0.5;
                                $emptyStars = 5 - $fullStars - ($hasHalfStar ? 1 : 0);
                            @endphp

                            @for ($i = 0; $i < $fullStars; $i++)
                                <i class="fas fa-star text-warning"></i>
                            @endfor

                            @if ($hasHalfStar)
                                <i class="fas fa-star-half-alt text-warning"></i>
                            @endif

                            @for ($i = 0; $i < $emptyStars; $i++)
                                <i class="far fa-star text-warning"></i>
                            @endfor

                            <span class="ms-1">{{ number_format($averageRating, 1) }}</span>
                        </div>
                        <span class="text-muted">({{ $reviewCount }}
                            {{ $reviewCount == 1 ? 'review' : 'reviews' }})</span>
                    </div>

                    <div class="mb-4">
                        @if ($product->isOnSale())
                            <div class="d-flex align-items-center">
                                <h3 class="fw-bold me-3 mb-0">₦{{ number_format($product->discount_price, 2) }}</h3>
                                <h5 class="text-muted text-decoration-line-through mb-0">
                                    ₦{{ number_format($product->price, 2) }}</h5>
                                <span class="badge bg-dark ms-3">{{ $product->getDiscountPercentage() }}% OFF</span>
                            </div>
                        @else
                            <h3 class="fw-bold mb-0">₦{{ number_format($product->price, 2) }}</h3>
                        @endif
                    </div>

                    <div class="mb-4">
                        <p class="mb-0">{{ $product->description }}</p>
                    </div>

                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-3 fw-bold">Availability:</span>
                            @if ($product->stock_quantity > 0)
                                <span class="text-success">In Stock ({{ $product->stock_quantity }} available)</span>
                            @else
                                <span class="text-danger">Out of Stock</span>
                            @endif
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-3 fw-bold">Category:</span>
                            <a href="{{ route('products.category', $product->category->slug) }}"
                                class="text-decoration-none">{{ $product->category->name }}</a>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <span class="me-3 fw-bold">Brand:</span>
                            <span>{{ $product->brand->name ?? 'N/A' }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="me-3 fw-bold">Vendor:</span>
                            <a href="{{ route('vendors.storefront', $product->vendor->slug) }}"
                                class="text-decoration-none">{{ $product->vendor->shop_name }}</a>
                        </div>
                    </div>

                    <hr>

                    <form action="{{ route('cart.add', $product->id) }}" method="POST" class="ajax-add-to-cart-form">
                        @csrf
                        <div class="mb-4">
                            <div class="d-flex align-items-center mb-3">
                                <span class="me-3 fw-bold">Quantity:</span>
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-outline-dark px-3"
                                        id="decrease-quantity">-</button>
                                    <input type="number" class="form-control text-center mx-2" name="quantity"
                                        id="quantity" value="1" min="1"
                                        max="{{ $product->stock_quantity > 0 ? $product->stock_quantity : 1 }}"
                                        style="width: 60px;" {{ $product->stock_quantity == 0 ? 'disabled' : '' }}>
                                    <button type="button" class="btn btn-outline-dark px-3"
                                        id="increase-quantity">+</button>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2 mb-4">
                            <button type="submit" class="btn btn-dark py-3 fw-bold add-to-cart-btn" id="add-to-cart"
                                {{ $product->stock_quantity == 0 ? 'disabled' : '' }}>
                                <i class="fas fa-shopping-cart me-2"></i>
                                {{ $product->stock_quantity == 0 ? 'Out of Stock' : 'Add to Cart' }}
                            </button>
                            <button type="button" class="btn btn-outline-dark py-3 fw-bold" id="add-to-wishlist">
                                <i class="far fa-heart me-2"></i> Add to Wishlist
                            </button>
                        </div>
                    </form>

                    <div class="d-flex justify-content-center">
                        <div class="d-flex align-items-center me-4">
                            <i class="fas fa-truck me-2"></i>
                            <span>Free Shipping</span>
                        </div>
                        <div class="d-flex align-items-center me-4">
                            <i class="fas fa-undo me-2"></i>
                            <span>30-Day Returns</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-shield-alt me-2"></i>
                            <span>Secure Checkout</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Tabs -->
    <div class="row mt-5">
        <div class="col-12">
            <ul class="nav nav-tabs" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active fw-bold" id="description-tab" data-bs-toggle="tab"
                        data-bs-target="#description" type="button" role="tab" aria-controls="description"
                        aria-selected="true">Description</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link fw-bold" id="specifications-tab" data-bs-toggle="tab"
                        data-bs-target="#specifications" type="button" role="tab" aria-controls="specifications"
                        aria-selected="false">Specifications</button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link fw-bold" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews"
                        type="button" role="tab" aria-controls="reviews" aria-selected="false">Reviews
                        ({{ $reviewCount }})</button>
                </li>
            </ul>
            <div class="tab-content p-4 border border-top-0 rounded-bottom" id="productTabsContent">
                <div class="tab-pane fade show active" id="description" role="tabpanel"
                    aria-labelledby="description-tab">
                    <p>{{ $product->description }}</p>
                </div>
                <div class="tab-pane fade" id="specifications" role="tabpanel" aria-labelledby="specifications-tab">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <th style="width: 30%;">Brand</th>
                                    <td>{{ $product->brand->name ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Model</th>
                                    <td>{{ $product->model ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Color</th>
                                    <td>{{ $product->color ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Weight</th>
                                    <td>{{ $product->weight ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Dimensions</th>
                                    <td>{{ $product->dimensions ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <th>Warranty</th>
                                    <td>{{ $product->warranty ?? 'N/A' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="tab-pane fade" id="reviews" role="tabpanel" aria-labelledby="reviews-tab">
                    <div class="mb-4">
                        <h5 class="fw-bold mb-3">Customer Reviews</h5>

                        @if ($reviews->count() > 0)
                            @foreach ($reviews as $review)
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between mb-2">
                                            <h6 class="fw-bold mb-0">{{ $review->user->name }}</h6>
                                            <small class="text-muted">{{ $review->created_at->format('M d, Y') }}</small>
                                        </div>
                                        <div class="mb-2">
                                            @for ($i = 1; $i <= 5; $i++)
                                                @if ($i <= $review->rating)
                                                    <i class="fas fa-star text-warning"></i>
                                                @else
                                                    <i class="far fa-star text-warning"></i>
                                                @endif
                                            @endfor
                                        </div>
                                        <p class="mb-0">{{ $review->comment }}</p>

                                        @if ($review->vendor_response)
                                            <div class="mt-3 p-3 bg-light rounded">
                                                <div class="d-flex justify-content-between mb-2">
                                                    <h6 class="fw-bold mb-0 text-primary">
                                                        <i
                                                            class="fas fa-store me-1"></i>{{ $product->vendor->shop_name ?? 'Vendor' }}
                                                        Response
                                                    </h6>
                                                    <small
                                                        class="text-muted">{{ $review->vendor_response_date->format('M d, Y') }}</small>
                                                </div>
                                                <p class="mb-0">{{ $review->vendor_response }}</p>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @endforeach

                            @if ($reviewCount > 5)
                                <div class="text-center">
                                    <button class="btn btn-outline-dark" id="load-more-reviews"
                                        data-product-id="{{ $product->id }}">
                                        Load More Reviews
                                    </button>
                                </div>
                            @endif
                        @else
                            <div class="text-center py-4">
                                <i class="fas fa-star-o fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No reviews yet</h6>
                                <p class="text-muted">Be the first to review this product!</p>
                            </div>
                        @endif
                    </div>

                    <div>
                        <h5 class="fw-bold mb-3">Write a Review</h5>

                        @if ($canReview)
                            <form action="{{ route('reviews.store', $product) }}" method="POST" id="review-form">
                                @csrf
                                <input type="hidden" name="product_id" value="{{ $product->id }}">
                                <input type="hidden" name="rating" id="rating-input" value="">

                                <div class="mb-3">
                                    <label for="rating" class="form-label">Rating <span
                                            class="text-danger">*</span></label>
                                    <div>
                                        <div class="rating">
                                            <i class="far fa-star fs-4 me-1" data-rating="1"></i>
                                            <i class="far fa-star fs-4 me-1" data-rating="2"></i>
                                            <i class="far fa-star fs-4 me-1" data-rating="3"></i>
                                            <i class="far fa-star fs-4 me-1" data-rating="4"></i>
                                            <i class="far fa-star fs-4" data-rating="5"></i>
                                        </div>
                                        <small class="text-muted">Click to rate this product</small>
                                    </div>
                                    @error('rating')
                                        <div class="text-danger small">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="mb-3">
                                    <label for="comment" class="form-label">Your Review <span
                                            class="text-danger">*</span></label>
                                    <textarea class="form-control @error('comment') is-invalid @enderror" id="comment" name="comment" rows="4"
                                        placeholder="Share your experience with this product..." required>{{ old('comment') }}</textarea>
                                    @error('comment')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <button type="submit" class="btn btn-dark">Submit Review</button>
                            </form>
                        @elseif(auth()->check())
                            @if ($product->hasUserReviewed(auth()->id()))
                                <div class="alert alert-info">
                                    <i class="fas fa-check-circle me-2"></i>
                                    You have already reviewed this product. Thank you for your feedback!
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="fas fa-shopping-cart me-2"></i>
                                    You can only review products you have purchased.
                                    <a href="#" class="alert-link">Purchase this product</a> to leave a review.
                                </div>
                            @endif
                        @else
                            <div class="alert alert-info">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                Please <a href="{{ route('login') }}" class="alert-link">login</a> to write a review.
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Products -->
    <div class="row mt-5">
        <div class="col-12">
            <h3 class="fw-bold mb-4">Related Products</h3>
        </div>

        @forelse($relatedProducts as $relatedProduct)
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card h-100 product-card">
                    <a href="{{ route('products.show', $relatedProduct->slug) }}">
                        <img src="{{ $relatedProduct->image_url ?? asset('images/default-product.png') }}"
                            class="card-img-top" alt="{{ $relatedProduct->name }}"
                            style="aspect-ratio: 1 / 1; object-fit: cover;">
                    </a>
                    <div class="card-body d-flex flex-column">
                        <h6 class="card-title mb-1">
                            <a href="{{ route('products.show', $relatedProduct->slug) }}"
                                class="text-dark text-decoration-none stretched-link">{{ Str::limit($relatedProduct->name, 35) }}</a>
                        </h6>
                        <p class="card-text text-muted small mb-2">{{ $relatedProduct->category->name ?? '' }}</p>
                        <div class="mt-auto">
                            <p class="fw-bold mb-2 text-dark">₦{{ number_format($relatedProduct->price, 2) }}</p>
                            <a href="{{ route('products.show', $relatedProduct->slug) }}"
                                class="btn btn-sm btn-outline-dark w-100">View Details</a>
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="col-12">
                <p class="text-muted">No related products found.</p>
            </div>
        @endforelse
    </div> <!-- End of Related Products .row -->

    <!-- Recently Viewed Products -->
    @if (isset($recentlyViewedProducts) && $recentlyViewedProducts->count() > 0)
        <div class="row mt-5">
            <div class="col-12">
                <h3 class="fw-bold mb-4">Recently Viewed</h3>
            </div>
            @foreach ($recentlyViewedProducts as $recentProduct)
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card h-100 product-card">
                        <a href="{{ route('products.show', $recentProduct->slug) }}">
                            <img src="{{ $recentProduct->image_url ?? asset('images/default-product.png') }}"
                                class="card-img-top" alt="{{ $recentProduct->name }}"
                                style="aspect-ratio: 1 / 1; object-fit: cover;">
                        </a>
                        <div class="card-body d-flex flex-column">
                            <h6 class="card-title mb-1">
                                <a href="{{ route('products.show', $recentProduct->slug) }}"
                                    class="text-dark text-decoration-none stretched-link">{{ Str::limit($recentProduct->name, 35) }}</a>
                            </h6>
                            <p class="card-text text-muted small mb-2">{{ $recentProduct->category->name ?? '' }}</p>
                            <div class="mt-auto">
                                <p class="fw-bold mb-2 text-dark">₦{{ number_format($recentProduct->price, 2) }}</p>
                                <a href="{{ route('products.show', $recentProduct->slug) }}"
                                    class="btn btn-sm btn-outline-dark w-100">View Details</a>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div> <!-- End of Recently Viewed Products .row -->
    @endif

    </div>

    @push('scripts')
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                // Quantity buttons
                const decreaseBtn = document.getElementById('decrease-quantity');
                const increaseBtn = document.getElementById('increase-quantity');
                const quantityInput = document.getElementById('quantity');
                const maxQuantity = {{ $product->stock_quantity }};

                decreaseBtn.addEventListener('click', function() {
                    let currentValue = parseInt(quantityInput.value);
                    if (currentValue > 1) {
                        quantityInput.value = currentValue - 1;
                    }
                });

                increaseBtn.addEventListener('click', function() {
                    let currentValue = parseInt(quantityInput.value);
                    if (currentValue < maxQuantity) {
                        quantityInput.value = currentValue + 1;
                    }
                });

                // Product image thumbnails
                const mainImage = document.getElementById('main-image');
                const thumbnails = document.querySelectorAll('.thumbnail');

                thumbnails.forEach(thumbnail => {
                    thumbnail.addEventListener('click', function() {
                        mainImage.src = this.src;

                        // Remove active class from all thumbnails
                        thumbnails.forEach(thumb => {
                            thumb.classList.remove('active');
                        });

                        // Add active class to clicked thumbnail
                        this.classList.add('active');
                    });
                });

                // Rating stars
                const ratingStars = document.querySelectorAll('.rating i');
                const ratingInput = document.getElementById('rating-input');
                let selectedRating = 0;

                ratingStars.forEach(star => {
                    star.addEventListener('mouseover', function() {
                        const rating = parseInt(this.dataset.rating);

                        ratingStars.forEach(s => {
                            const starRating = parseInt(s.dataset.rating);
                            if (starRating <= rating) {
                                s.classList.remove('far');
                                s.classList.add('fas');
                            } else {
                                s.classList.remove('fas');
                                s.classList.add('far');
                            }
                        });
                    });

                    star.addEventListener('mouseout', function() {
                        ratingStars.forEach(s => {
                            const starRating = parseInt(s.dataset.rating);
                            if (starRating <= selectedRating) {
                                s.classList.remove('far');
                                s.classList.add('fas');
                            } else {
                                s.classList.remove('fas');
                                s.classList.add('far');
                            }
                        });
                    });

                    star.addEventListener('click', function() {
                        selectedRating = parseInt(this.dataset.rating);
                        if (ratingInput) {
                            ratingInput.value = selectedRating;
                        }
                    });
                });

                // Review form validation
                const reviewForm = document.getElementById('review-form');
                if (reviewForm) {
                    reviewForm.addEventListener('submit', function(e) {
                        if (selectedRating === 0) {
                            e.preventDefault();
                            alert('Please select a rating before submitting your review.');
                            return false;
                        }
                    });
                }

                // Load more reviews functionality
                const loadMoreBtn = document.getElementById('load-more-reviews');
                if (loadMoreBtn) {
                    loadMoreBtn.addEventListener('click', function() {
                        const productId = this.dataset.productId;
                        // This would typically load more reviews via AJAX
                        // For now, we'll just redirect to show all reviews
                        window.location.href = `{{ route('reviews.get', $product) }}`;
                    });
                }
            });
        </script>
    @endpush
@endsection
