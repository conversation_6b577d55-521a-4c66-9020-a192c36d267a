@extends('layouts.vendor')

@section('content')
<div class="container-fluid">
    <div class="mb-4">
        <a href="{{ route('vendor.products.index') }}" class="text-decoration-none">
            <i class="fas fa-arrow-left me-1"></i> Back to Products
        </a>
    </div>
    
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Edit Product</h1>
    </div>

    <div class="card border-0 shadow-sm">
        <div class="card-body p-4">
            <form action="{{ route('vendor.products.update', $product->id) }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="row mb-4">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="name" class="form-label fw-bold">Product Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name', $product->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="description" class="form-label fw-bold">Description <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="6" required>{{ old('description', $product->description) }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="image" class="form-label fw-bold">Product Image</label>
                            <input type="file" class="form-control @error('image') is-invalid @enderror" id="image" name="image">
                            <small class="text-muted">Recommended size: 800x800px, max 2MB</small>
                            @error('image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3 mt-4">
                            <div class="border rounded p-3 text-center" id="image-preview">
                                <img src="{{ $product->image_url ?? 'https://via.placeholder.com/400x400?text=No+Image' }}" id="preview" class="img-fluid rounded" alt="Product Image">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="category_id" class="form-label fw-bold">Category <span class="text-danger">*</span></label>
                            <select class="form-select @error('category_id') is-invalid @enderror" id="category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ old('category_id', $product->category_id) == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('category_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="brand_id" class="form-label fw-bold">Brand</label>
                            <select class="form-select @error('brand_id') is-invalid @enderror" id="brand_id" name="brand_id">
                                <option value="">Select Brand</option>
                                @foreach($brands as $brand)
                                    <option value="{{ $brand->id }}" {{ old('brand_id', $product->brand_id) == $brand->id ? 'selected' : '' }}>
                                        {{ $brand->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('brand_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="sku" class="form-label fw-bold">SKU</label>
                            <input type="text" class="form-control @error('sku') is-invalid @enderror" id="sku" name="sku" value="{{ old('sku', $product->sku) }}">
                            @error('sku')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="price" class="form-label fw-bold">Price ($) <span class="text-danger">*</span></label>
                            <input type="number" step="0.01" class="form-control @error('price') is-invalid @enderror" id="price" name="price" value="{{ old('price', $product->price) }}" required>
                            @error('price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="discount_price" class="form-label fw-bold">Discount Price ($)</label>
                            <input type="number" step="0.01" class="form-control @error('discount_price') is-invalid @enderror" id="discount_price" name="discount_price" value="{{ old('discount_price', $product->discount_price) }}">
                            @error('discount_price')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label for="stock_quantity" class="form-label fw-bold">Stock Quantity <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('stock_quantity') is-invalid @enderror" id="stock_quantity" name="stock_quantity" value="{{ old('stock_quantity', $product->stock_quantity) }}" required>
                            @error('stock_quantity')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="col-md-3">
                        <div class="mb-3">
                            <label class="form-label fw-bold">Status</label>
                            <div class="form-check form-switch mt-2">
                                <input class="form-check-input" type="checkbox" id="is_active" name="is_active" {{ $product->is_active ? 'checked' : '' }}>
                                <label class="form-check-label" for="is_active">Active</label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-4">
                    <div class="col-12">
                        <label class="form-label fw-bold">Specifications/Attributes</label>
                        <div class="table-responsive mb-2">
                            <table class="table table-bordered" id="attributes-table">
                                <thead class="bg-light">
                                    <tr>
                                        <th>Attribute Name</th>
                                        <th>Value</th>
                                        <th style="width: 50px;"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if($product->attributes && count($product->attributes) > 0)
                                        @foreach($product->attributes as $index => $attribute)
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control" name="attributes[{{ $index }}][name]" value="{{ $attribute->name }}" placeholder="e.g. Color, Size, Material">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="attributes[{{ $index }}][value]" value="{{ $attribute->value }}" placeholder="e.g. Red, XL, Cotton">
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-attribute"><i class="fas fa-times"></i></button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td>
                                                <input type="text" class="form-control" name="attributes[0][name]" placeholder="e.g. Color, Size, Material">
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="attributes[0][value]" placeholder="e.g. Red, XL, Cotton">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-danger remove-attribute"><i class="fas fa-times"></i></button>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-dark" id="add-attribute">
                            <i class="fas fa-plus me-1"></i> Add Attribute
                        </button>
                    </div>
                </div>

                {{-- Product Variants Section --}}
                <div class="row mb-4">
                    <div class="col-12">
                        <h5 class="mb-3 fw-bold">Product Variants</h5>
                        <p class="text-muted small">
                            Manage color and size options for this product. Each variant can have its own SKU, price adjustment, stock level, and image.
                        </p>
                        <div id="variants-container">
                            {{-- Existing and new variant rows will be appended here --}}
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" id="add-variant-btn">
                            <i class="fas fa-plus me-1"></i> Add New Variant
                        </button>
                    </div>
                </div>

                {{-- Hidden Template for a Single Variant Row --}}
                <template id="variant-row-template">
                    <div class="variant-row border rounded p-3 mb-3 bg-light">
                        <input type="hidden" name="variants[__INDEX__][id]" value=""> {{-- Populated for existing variants --}}
                        <div class="row align-items-center">
                            <div class="col-md-3 mb-2">
                                <label class="form-label small">Color</label>
                                <select name="variants[__INDEX__][color_id]" class="form-select form-select-sm variant-color">
                                    <option value="">None</option>
                                    @foreach($colors as $color)
                                        <option value="{{ $color->id }}">{{ $color->name }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-3 mb-2">
                                <label class="form-label small">Size</label>
                                <select name="variants[__INDEX__][size_id]" class="form-select form-select-sm variant-size">
                                    <option value="">None</option>
                                    @foreach($sizes as $size)
                                        <option value="{{ $size->id }}">{{ $size->name }} {{ $size->code ? '('.$size->code.')' : '' }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2 mb-2">
                                <label class="form-label small">SKU</label>
                                <input type="text" name="variants[__INDEX__][sku]" class="form-control form-control-sm variant-sku" placeholder="Variant SKU">
                            </div>
                            <div class="col-md-2 mb-2">
                                <label class="form-label small">Price Adj. ($)</label>
                                <input type="number" step="0.01" name="variants[__INDEX__][price_adjustment]" class="form-control form-control-sm variant-price-adj" placeholder="e.g. -2.00">
                            </div>
                            <div class="col-md-2 mb-2">
                                <label class="form-label small">Stock <span class="text-danger">*</span></label>
                                <input type="number" name="variants[__INDEX__][stock_quantity]" class="form-control form-control-sm variant-stock" placeholder="Stock" value="0" required>
                            </div>
                        </div>
                        <div class="row align-items-center mt-2">
                             <div class="col-md-10 mb-2">
                                <label class="form-label small">Variant Image <small class="text-muted">(Leave blank to keep existing)</small></label>
                                <input type="file" name="variants[__INDEX__][image]" class="form-control form-control-sm variant-image">
                                {{-- Placeholder for current image preview if needed --}}
                                <input type="hidden" name="variants[__INDEX__][current_image_path]" class="variant-current-image">
                            </div>
                            <div class="col-md-2 d-flex align-items-end mb-2">
                                <button type="button" class="btn btn-sm btn-outline-danger remove-variant-btn w-100">
                                    <i class="fas fa-times"></i> Remove
                                </button>
                            </div>
                        </div>
                         <hr class="mt-3 mb-0">
                    </div>
                </template>

                <div class="row mb-4">
                    <div class="col-12">
                        <label class="form-label fw-bold">Specifications/Attributes</label>
                        <div class="table-responsive mb-2">
                            <table class="table table-bordered" id="attributes-table">
                                <thead class="bg-light">
                                    <tr>
                                        <th>Attribute Name</th>
                                        <th>Value</th>
                                        <th style="width: 50px;"></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if($product->attributes && count($product->attributes) > 0)
                                        @foreach($product->attributes as $index => $attribute)
                                            <tr>
                                                <td>
                                                    <input type="text" class="form-control" name="attributes[{{ $index }}][name]" value="{{ $attribute->name }}" placeholder="e.g. Color, Size, Material">
                                                </td>
                                                <td>
                                                    <input type="text" class="form-control" name="attributes[{{ $index }}][value]" value="{{ $attribute->value }}" placeholder="e.g. Red, XL, Cotton">
                                                </td>
                                                <td>
                                                    <button type="button" class="btn btn-sm btn-outline-danger remove-attribute"><i class="fas fa-times"></i></button>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @else
                                        <tr>
                                            <td>
                                                <input type="text" class="form-control" name="attributes[0][name]" placeholder="e.g. Color, Size, Material">
                                            </td>
                                            <td>
                                                <input type="text" class="form-control" name="attributes[0][value]" placeholder="e.g. Red, XL, Cotton">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-danger remove-attribute"><i class="fas fa-times"></i></button>
                                            </td>
                                        </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        <button type="button" class="btn btn-sm btn-outline-dark" id="add-attribute">
                            <i class="fas fa-plus me-1"></i> Add Attribute
                        </button>
                    </div>
                </div>
                
                <div class="d-flex justify-content-end mt-4">
                    <a href="{{ route('vendor.products.index') }}" class="btn btn-outline-secondary me-2">Cancel</a>
                    <button type="submit" class="btn btn-dark">Update Product</button>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Main Product Image preview
        const imageInput = document.getElementById('image');
        const imagePreview = document.getElementById('preview');
        if (imageInput && imagePreview) {
            imageInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const reader = new FileReader();
                    reader.onload = function(e) { imagePreview.src = e.target.result; }
                    reader.readAsDataURL(this.files[0]);
                } else {
                    // Revert to original product image URL if available, else placeholder
                    imagePreview.src = "{{ $product->image_url ?? 'https://via.placeholder.com/400x400?text=No+Image' }}";
                }
            });
        }

        // Attributes Management
        const attributesTableBody = document.querySelector('#attributes-table tbody');
        const addAttributeButton = document.getElementById('add-attribute');
        let attributeCounter = {{ $product->attributes && $product->attributes->count() > 0 ? $product->attributes->count() : 1 }}; 
        if (addAttributeButton && attributesTableBody) {
            addAttributeButton.addEventListener('click', function() {
                const newRowHTML = `
                    <tr>
                        <td><input type="text" class="form-control" name="attributes[${attributeCounter}][name]" placeholder="e.g. Color, Size, Material"></td>
                        <td><input type="text" class="form-control" name="attributes[${attributeCounter}][value]" placeholder="e.g. Red, XL, Cotton"></td>
                        <td><button type="button" class="btn btn-sm btn-outline-danger remove-attribute"><i class="fas fa-times"></i></button></td>
                    </tr>`;
                attributesTableBody.insertAdjacentHTML('beforeend', newRowHTML);
                attributeCounter++;
            });
            attributesTableBody.addEventListener('click', function(e) {
                if (e.target.classList.contains('remove-attribute') || e.target.closest('.remove-attribute')) {
                    e.target.closest('tr').remove();
                }
            });
        }

        // Product Variant Management
        const variantsContainer = document.getElementById('variants-container');
        const addVariantBtn = document.getElementById('add-variant-btn');
        const variantRowTemplate = document.getElementById('variant-row-template');
        let variantIndex = 0; 

        function addVariantRow(variantData = null) {
            const templateNode = variantRowTemplate.content.cloneNode(true);
            const newVariantRow = templateNode.firstElementChild;

            const newIndex = variantIndex; // Use current variantIndex for this new row

            newVariantRow.querySelectorAll('[name]').forEach(input => {
                input.name = input.name.replace('__INDEX__', newIndex);
                if (input.id) input.id = input.id.replace('__INDEX__', newIndex);
            });
            newVariantRow.querySelectorAll('[for]').forEach(label => {
                if (label.htmlFor) label.htmlFor = label.htmlFor.replace('__INDEX__', newIndex);
            });

            if (variantData) {
                newVariantRow.querySelector('input[name$="[id]"]').value = variantData.id || '';
                newVariantRow.querySelector('select[name$="[color_id]"]').value = variantData.color_id || '';
                newVariantRow.querySelector('select[name$="[size_id]"]').value = variantData.size_id || '';
                newVariantRow.querySelector('input[name$="[sku]"]').value = variantData.sku || '';
                newVariantRow.querySelector('input[name$="[price_adjustment]"]').value = variantData.price_adjustment || '';
                newVariantRow.querySelector('input[name$="[stock_quantity]"]').value = variantData.stock_quantity || 0;
                newVariantRow.querySelector('input[name$="[current_image_path]"]').value = variantData.image_path || '';
                // Add a small text if current_image_path exists
                if (variantData.image_path) {
                    const imgLabel = newVariantRow.querySelector('label[for*="image"]');
                    if(imgLabel && imgLabel.parentElement) {
                        const currentImageText = document.createElement('small');
                        currentImageText.className = 'd-block text-muted mt-1';
                        currentImageText.textContent = 'Current: ' + variantData.image_path.split('/').pop();
                        imgLabel.parentElement.appendChild(currentImageText);
                    }
                }
            }

            variantsContainer.appendChild(newVariantRow);
            variantIndex++; // Increment global index for the next row
        }

        if (addVariantBtn && variantsContainer && variantRowTemplate) {
            // Load existing variants
            const existingVariants = @json($product->variants ?? []);
            existingVariants.forEach(variant => {
                addVariantRow(variant);
            });

            addVariantBtn.addEventListener('click', function() {
                addVariantRow(); // Add a new empty row
            });

            variantsContainer.addEventListener('click', function(event) {
                let targetElement = event.target;
                if (targetElement.tagName === 'I' && targetElement.parentElement.classList.contains('remove-variant-btn')) {
                    targetElement = targetElement.parentElement;
                }
                if (targetElement.classList.contains('remove-variant-btn')) {
                    const rowToRemove = targetElement.closest('.variant-row');
                    if (rowToRemove) {
                        // If it's an existing variant (has an ID), we might mark it for deletion later
                        // For now, just remove from DOM. Backend will handle persistence.
                        rowToRemove.remove(); 
                    }
                }
            });
        }
    });
</script>
@endpush

@endsection
