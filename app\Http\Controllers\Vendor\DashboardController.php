<?php

namespace App\Http\Controllers\Vendor;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\Product;
use App\Models\Commission;
use App\Models\Subscription;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    /**
     * Display the vendor dashboard with stats
     */
    public function index()
    {
        $vendor = auth()->user()->vendor;
        
        // Get total sales
        $totalSales = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->sum(DB::raw('order_items.price * order_items.quantity'));
            
        // Get last month's sales for comparison
        $lastMonthSales = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->where('orders.created_at', '>=', Carbon::now()->subMonth())
            ->sum(DB::raw('order_items.price * order_items.quantity'));
            
        $prevMonthSales = $vendor->products()
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [Carbon::now()->subMonths(2), Carbon::now()->subMonth()])
            ->sum(DB::raw('order_items.price * order_items.quantity'));
        
        // Calculate growth percentage
        $salesGrowth = $prevMonthSales > 0 
            ? round((($lastMonthSales - $prevMonthSales) / $prevMonthSales) * 100, 1) 
            : 100;
        
        // Count total orders
        $totalOrders = Order::whereHas('items.product', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })->count();
        
        // Count last month's orders
        $lastMonthOrders = Order::whereHas('items.product', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
        ->where('created_at', '>=', Carbon::now()->subMonth())
        ->count();
        
        $prevMonthOrders = Order::whereHas('items.product', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
        ->whereBetween('created_at', [Carbon::now()->subMonths(2), Carbon::now()->subMonth()])
        ->count();
        
        // Calculate order growth percentage
        $orderGrowth = $prevMonthOrders > 0 
            ? round((($lastMonthOrders - $prevMonthOrders) / $prevMonthOrders) * 100, 1) 
            : 100;
        
        // Count total products
        $totalProducts = $vendor->products()->count();
        $newProductsThisMonth = $vendor->products()
            ->where('created_at', '>=', Carbon::now()->startOfMonth())
            ->count();
        
        // Get subscription info
        $subscription = $vendor->subscription;
        $subscriptionName = $subscription ? $subscription->plan->name : 'Free';
        $daysRemaining = $subscription ? Carbon::now()->diffInDays($subscription->end_date, false) : 0;
        
        // Recent orders
        $recentOrders = Order::whereHas('items.product', function($query) use ($vendor) {
            $query->where('vendor_id', $vendor->id);
        })
        ->with(['items.product', 'user'])
        ->latest()
        ->limit(5)
        ->get();
        
        // Recent product performance
        $productPerformance = $vendor->products()
            ->withCount(['orderItems as total_sold'])
            ->orderBy('total_sold', 'desc')
            ->limit(5)
            ->get();
        
        return view('vendor.dashboard', compact(
            'totalSales', 'salesGrowth', 'totalOrders', 'orderGrowth',
            'totalProducts', 'newProductsThisMonth', 'subscriptionName',
            'daysRemaining', 'recentOrders', 'productPerformance'
        ));
    }
}
