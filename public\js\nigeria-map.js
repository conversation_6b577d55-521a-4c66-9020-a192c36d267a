/**
 * Nigeria Map Widget for Vendor Dashboard
 * This widget creates an interactive SVG map of Nigeria with state highlighting
 * based on order distribution data.
 */

class NigeriaMapWidget {
    constructor(elementId, orderData) {
        this.container = document.getElementById(elementId);
        this.orderData = orderData || {};
        this.maxOrders = 0;
        this.initMap();
    }

    initMap() {
        // Find maximum order count for scale normalization
        if (Object.keys(this.orderData).length > 0) {
            this.maxOrders = Math.max(...Object.values(this.orderData));
        }

        // Create SVG container
        this.svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        this.svg.setAttribute('viewBox', '0 0 800 900');
        this.svg.setAttribute('class', 'nigeria-map');
        this.container.appendChild(this.svg);

        // Load and render the map
        this.renderMap();
    }

    // Get color intensity based on order count
    getStateColor(stateName) {
        const orderCount = this.orderData[stateName] || 0;
        
        if (orderCount === 0) {
            return '#f5f5f5'; // Light gray for states with no orders
        }
        
        // Normalize order count and get appropriate color intensity
        const normalized = this.maxOrders ? orderCount / this.maxOrders : 0;
        const intensity = Math.min(Math.floor(normalized * 100), 100);
        
        // Return color from light blue (few orders) to dark blue (many orders)
        return `rgba(0, 0, 0, ${0.1 + (normalized * 0.8)})`;
    }

    // Add pulse animation to states with orders
    addAnimation(path, stateName) {
        const orderCount = this.orderData[stateName] || 0;
        
        if (orderCount > 0) {
            // Add pulse animation class with delay based on order count
            const delay = Math.random() * 2; // Random delay for more natural effect
            path.style.animation = `pulse 2s infinite ${delay}s`;
        }
    }

    renderMap() {
        // Nigeria states data with SVG paths
        const statesData = {
            'Abia': 'M430,460 L455,470 L470,490 L455,515 L430,510 L410,490 L415,460 Z',
            'Adamawa': 'M520,230 L550,220 L580,230 L590,260 L580,290 L550,300 L520,290 L510,260 Z',
            'Akwa Ibom': 'M445,530 L470,525 L490,540 L485,565 L465,575 L445,565 L435,545 Z',
            'Anambra': 'M405,450 L425,445 L440,460 L435,485 L415,490 L400,475 Z',
            'Bauchi': 'M460,200 L490,190 L520,200 L525,230 L505,245 L475,240 L455,225 Z',
            'Bayelsa': 'M385,520 L410,510 L430,520 L425,545 L405,555 L385,545 Z',
            'Benue': 'M440,335 L470,325 L500,335 L505,360 L485,375 L455,370 L435,355 Z',
            'Borno': 'M530,130 L580,120 L620,150 L625,200 L600,230 L555,220 L525,185 L520,150 Z',
            'Cross River': 'M460,460 L485,450 L505,470 L510,500 L490,525 L470,515 L465,485 Z',
            'Delta': 'M375,465 L405,455 L430,470 L425,495 L400,510 L375,495 Z',
            'Ebonyi': 'M445,435 L465,425 L480,440 L475,465 L455,470 L440,455 Z',
            'Edo': 'M385,410 L415,400 L440,415 L435,445 L410,460 L385,445 Z',
            'Ekiti': 'M345,400 L365,390 L380,405 L375,425 L355,430 L340,415 Z',
            'Enugu': 'M425,425 L450,415 L465,430 L460,455 L435,460 L420,445 Z',
            'FCT': 'M400,330 L420,325 L435,335 L430,355 L410,360 L395,350 Z',
            'Gombe': 'M490,210 L520,200 L545,210 L550,235 L530,250 L505,245 L495,230 Z',
            'Imo': 'M420,480 L445,470 L465,480 L460,505 L440,515 L420,505 Z',
            'Jigawa': 'M430,150 L460,140 L480,150 L485,175 L465,185 L445,175 L435,160 Z',
            'Kaduna': 'M380,230 L410,220 L435,235 L430,265 L405,275 L380,265 Z',
            'Kano': 'M400,180 L430,170 L455,180 L450,205 L425,215 L400,205 Z',
            'Katsina': 'M350,170 L380,160 L405,170 L400,195 L375,205 L350,195 Z',
            'Kebbi': 'M290,200 L320,190 L345,200 L340,225 L315,235 L290,225 Z',
            'Kogi': 'M380,350 L410,340 L435,350 L430,375 L405,385 L380,375 Z',
            'Kwara': 'M335,320 L365,310 L390,320 L385,345 L360,355 L335,345 Z',
            'Lagos': 'M300,420 L325,415 L340,425 L335,445 L315,450 L300,440 Z',
            'Nasarawa': 'M420,310 L450,300 L475,310 L470,335 L445,345 L420,335 Z',
            'Niger': 'M340,270 L375,260 L405,270 L400,295 L370,305 L340,295 Z',
            'Ogun': 'M320,405 L345,395 L365,405 L360,430 L335,440 L320,425 Z',
            'Ondo': 'M355,430 L380,420 L400,430 L395,455 L370,465 L355,450 Z',
            'Osun': 'M340,375 L365,365 L385,375 L380,400 L355,410 L340,395 Z',
            'Oyo': 'M305,365 L335,355 L360,365 L355,390 L330,400 L310,385 Z',
            'Plateau': 'M450,270 L480,260 L505,270 L500,295 L475,305 L450,295 Z',
            'Rivers': 'M415,500 L440,490 L460,500 L455,525 L430,535 L415,525 Z',
            'Sokoto': 'M265,170 L295,160 L320,170 L315,195 L290,205 L270,195 Z',
            'Taraba': 'M505,315 L535,305 L560,315 L555,340 L530,350 L505,340 Z',
            'Yobe': 'M495,160 L525,150 L550,160 L545,185 L520,195 L495,185 Z',
            'Zamfara': 'M330,205 L360,195 L385,205 L380,230 L355,240 L330,230 Z'
        };

        // Render each state
        for (const [stateName, path] of Object.entries(statesData)) {
            const statePath = document.createElementNS('http://www.w3.org/2000/svg', 'path');
            statePath.setAttribute('d', path);
            statePath.setAttribute('class', 'state');
            statePath.setAttribute('data-state', stateName);
            statePath.setAttribute('fill', this.getStateColor(stateName));
            statePath.setAttribute('stroke', '#ffffff');
            statePath.setAttribute('stroke-width', '1');
            
            // Add tooltip
            const orderCount = this.orderData[stateName] || 0;
            statePath.setAttribute('data-tooltip', `${stateName}: ${orderCount} orders`);
            
            // Add hover effect
            statePath.addEventListener('mouseover', () => {
                statePath.setAttribute('stroke-width', '2');
                
                // Show tooltip
                const tooltip = document.createElement('div');
                tooltip.className = 'map-tooltip';
                tooltip.textContent = statePath.getAttribute('data-tooltip');
                tooltip.style.position = 'absolute';
                tooltip.style.left = (event.pageX + 10) + 'px';
                tooltip.style.top = (event.pageY + 10) + 'px';
                document.body.appendChild(tooltip);
                
                // Store reference to remove later
                statePath.tooltip = tooltip;
            });
            
            statePath.addEventListener('mousemove', (event) => {
                if (statePath.tooltip) {
                    statePath.tooltip.style.left = (event.pageX + 10) + 'px';
                    statePath.tooltip.style.top = (event.pageY + 10) + 'px';
                }
            });
            
            statePath.addEventListener('mouseout', () => {
                statePath.setAttribute('stroke-width', '1');
                if (statePath.tooltip) {
                    document.body.removeChild(statePath.tooltip);
                    statePath.tooltip = null;
                }
            });
            
            // Add animation
            this.addAnimation(statePath, stateName);
            
            this.svg.appendChild(statePath);
            
            // Add state label for states with orders
            if (orderCount > 0) {
                const centroid = this.getCentroid(path);
                const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
                text.setAttribute('x', centroid.x);
                text.setAttribute('y', centroid.y);
                text.setAttribute('text-anchor', 'middle');
                text.setAttribute('font-size', '10');
                text.setAttribute('fill', '#000');
                text.textContent = stateName;
                this.svg.appendChild(text);
            }
        }
        
        // Add map legend
        this.addLegend();
    }
    
    // Calculate rough centroid of an SVG path
    getCentroid(path) {
        // Simple implementation: average of all coordinates
        const coords = path.match(/\d+/g).map(Number);
        const points = [];
        
        for (let i = 0; i < coords.length; i += 2) {
            points.push({x: coords[i], y: coords[i+1]});
        }
        
        const x = points.reduce((sum, p) => sum + p.x, 0) / points.length;
        const y = points.reduce((sum, p) => sum + p.y, 0) / points.length;
        
        return {x, y};
    }
    
    // Add a legend to explain the color scale
    addLegend() {
        const legend = document.createElementNS('http://www.w3.org/2000/svg', 'g');
        legend.setAttribute('class', 'legend');
        legend.setAttribute('transform', 'translate(650, 50)');
        
        // Legend title
        const title = document.createElementNS('http://www.w3.org/2000/svg', 'text');
        title.setAttribute('x', 0);
        title.setAttribute('y', 0);
        title.setAttribute('font-size', '12');
        title.setAttribute('font-weight', 'bold');
        title.textContent = 'Orders by State';
        legend.appendChild(title);
        
        // Legend items
        const items = [
            {label: 'No orders', color: '#f5f5f5'},
            {label: 'Few', color: 'rgba(0, 0, 0, 0.2)'},
            {label: 'Medium', color: 'rgba(0, 0, 0, 0.5)'},
            {label: 'Many', color: 'rgba(0, 0, 0, 0.9)'}
        ];
        
        items.forEach((item, index) => {
            const y = 20 + (index * 20);
            
            // Color box
            const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect');
            rect.setAttribute('x', 0);
            rect.setAttribute('y', y);
            rect.setAttribute('width', 15);
            rect.setAttribute('height', 15);
            rect.setAttribute('fill', item.color);
            rect.setAttribute('stroke', '#000');
            rect.setAttribute('stroke-width', '0.5');
            legend.appendChild(rect);
            
            // Label
            const text = document.createElementNS('http://www.w3.org/2000/svg', 'text');
            text.setAttribute('x', 25);
            text.setAttribute('y', y + 12);
            text.setAttribute('font-size', '10');
            text.textContent = item.label;
            legend.appendChild(text);
        });
        
        this.svg.appendChild(legend);
    }
    
    // Update the map with new order data
    updateData(newOrderData) {
        this.orderData = newOrderData;
        this.maxOrders = Math.max(...Object.values(this.orderData));
        
        // Update state colors and animations
        const statePaths = this.svg.querySelectorAll('.state');
        statePaths.forEach(path => {
            const stateName = path.getAttribute('data-state');
            path.setAttribute('fill', this.getStateColor(stateName));
            path.setAttribute('data-tooltip', `${stateName}: ${this.orderData[stateName] || 0} orders`);
            
            // Clear existing animation
            path.style.animation = '';
            
            // Reapply animation
            this.addAnimation(path, stateName);
        });
    }
}

// Add CSS for animations
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% {
                opacity: 1;
                stroke-width: 1;
            }
            50% {
                opacity: 0.8;
                stroke-width: 2;
            }
            100% {
                opacity: 1;
                stroke-width: 1;
            }
        }
        
        .map-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
        }
        
        .nigeria-map {
            max-width: 100%;
            height: auto;
        }
        
        .state {
            transition: fill 0.3s ease;
            cursor: pointer;
        }
        
        .state:hover {
            fill-opacity: 0.8;
        }
    `;
    document.head.appendChild(style);
});
