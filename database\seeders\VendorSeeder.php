<?php

namespace Database\Seeders;


use Illuminate\Database\Seeder;
use App\Models\Vendor;
use App\Models\User;
use Illuminate\Support\Str;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Nike vendor/brand
        $this->createVendor(
            'Nike',
            'Leading sportswear and athletic footwear brand known for innovation and performance.',
            'New York',
            'NY',
            'USA',
            'nike-logo.png',
            true
        );

        // Create Adidas vendor/brand
        $this->createVendor(
            'Adidas',
            'Iconic sportswear brand combining style with performance for athletes and fashion enthusiasts.',
            'Berlin',
            'Berlin',
            'Germany',
            'adidas-logo.png',
            true
        );

        // Create Puma vendor/brand
        $this->createVendor(
            'Puma',
            'Sports lifestyle brand offering innovative designs for both athletic and casual wear.',
            'Herzogenaurach',
            'Bavaria',
            'Germany',
            'puma-logo.png',
            true
        );

        // Create additional clothing brand vendors
        $this->createVendor(
            'Elegance Fashion',
            'Luxury fashion and accessories with timeless designs.',
            'Lagos',
            'Lagos',
            'Nigeria',
            'elegance-logo.png',
            true
        );

        $this->createVendor(
            'Urban Threads',
            'Contemporary street fashion with urban aesthetics.',
            'Abuja',
            'FCT',
            'Nigeria',
            'urban-threads-logo.png',
            false
        );
    }

    /**
     * Create a vendor/brand with the given details
     */
    private function createVendor($name, $description, $city, $state, $country, $logo, $featured = false)
    {
        // Get vendor role
        $vendorRole = \App\Models\Role::where('name', 'vendor')->first();

        // Create a user for the vendor if it doesn't exist
        $email = Str::slug($name) . '@example.com';
        $user = User::firstOrCreate(
            ['email' => $email],
            [
                'name' => $name . ' Admin',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
                'role_id' => $vendorRole ? $vendorRole->id : 2
            ]
        );

        // Create or update the vendor (which is also the brand)
        Vendor::updateOrCreate(
            ['user_id' => $user->id],
            [
                'shop_name' => $name,
                'slug' => Str::slug($name),
                'description' => $description,
                'address' => '123 Business Street',
                'city' => $city,
                'state' => $state,
                'country' => $country,
                'logo' => $logo,
                'approved' => true,
                'is_featured' => $featured
            ]
        );
    }
}
