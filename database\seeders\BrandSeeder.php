<?php

namespace Database\Seeders;

use App\Models\Brand;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class BrandSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample brands
        $brands = [
            [
                'name' => 'Elegance',
                'description' => 'Luxury fashion and accessories with timeless designs.',
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'TechStyle',
                'description' => 'Cutting-edge tech accessories with stylish designs.',
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Urban Threads',
                'description' => 'Contemporary street fashion with urban aesthetics.',
                'is_active' => true,
                'is_featured' => true,
            ],
            [
                'name' => 'Nature\'s Touch',
                'description' => 'Eco-friendly products made with sustainable materials.',
                'is_active' => true,
                'is_featured' => false,
            ],
            [
                'name' => 'Classic Heritage',
                'description' => 'Traditional designs with modern craftsmanship.',
                'is_active' => true,
                'is_featured' => false,
            ],
        ];

        foreach ($brands as $brandData) {
            Brand::firstOrCreate([
                'slug' => Str::slug($brandData['name']),
            ], [
                'name' => $brandData['name'],
                'description' => $brandData['description'],
                'logo' => null, // We'll use the placeholder for now
                'is_active' => $brandData['is_active'],
                'is_featured' => $brandData['is_featured'],
            ]);
        }
    }
}
